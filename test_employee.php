<?php
require_once 'database.php';

// Test data for employees
$testEmployees = [
    [
        'employee_id' => 'EMP001',
        'first_name' => '<PERSON>',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'phone' => '******-0101',
        'position' => 'Software Engineer',
        'department' => 'IT',
        'hire_date' => '2023-01-15',
        'salary' => 75000.00,
        'status' => 'Active'
    ],
    [
        'employee_id' => 'EMP002',
        'first_name' => '<PERSON>',
        'last_name' => '<PERSON>',
        'email' => '<EMAIL>',
        'phone' => '******-0102',
        'position' => 'Project Manager',
        'department' => 'Administration',
        'hire_date' => '2022-08-20',
        'salary' => 85000.00,
        'status' => 'Active'
    ],
    [
        'employee_id' => 'DOC001',
        'first_name' => 'Dr. <PERSON>',
        'last_name' => 'Johnson',
        'email' => 'micha<PERSON>.<EMAIL>',
        'phone' => '******-0103',
        'position' => 'Chief Medical Officer',
        'department' => 'Medical',
        'hire_date' => '2020-03-10',
        'salary' => 150000.00,
        'status' => 'Active'
    ],
    [
        'employee_id' => 'NUR001',
        'first_name' => 'Sarah',
        'last_name' => 'Williams',
        'email' => '<EMAIL>',
        'phone' => '******-0104',
        'position' => 'Head Nurse',
        'department' => 'Nursing',
        'hire_date' => '2021-06-01',
        'salary' => 65000.00,
        'status' => 'Active'
    ],
    [
        'employee_id' => 'EMP003',
        'first_name' => 'Robert',
        'last_name' => 'Brown',
        'email' => '<EMAIL>',
        'phone' => '******-0105',
        'position' => 'Maintenance Supervisor',
        'department' => 'Maintenance',
        'hire_date' => '2019-11-15',
        'salary' => 55000.00,
        'status' => 'Inactive'
    ]
];

echo "<h2>Testing Employee CRUD System</h2>";
echo "<p>Adding test employees...</p>";

$successCount = 0;
$errorCount = 0;

foreach ($testEmployees as $employee) {
    $result = insertEmployee($conn, $employee);
    if ($result['success']) {
        echo "<p style='color: green;'>✓ Added: {$employee['first_name']} {$employee['last_name']} ({$employee['employee_id']})</p>";
        $successCount++;
    } else {
        echo "<p style='color: red;'>✗ Failed to add: {$employee['first_name']} {$employee['last_name']} - {$result['message']}</p>";
        $errorCount++;
    }
}

echo "<hr>";
echo "<h3>Summary:</h3>";
echo "<p>Successfully added: <strong>$successCount</strong> employees</p>";
echo "<p>Errors: <strong>$errorCount</strong></p>";

// Test search functionality
echo "<hr>";
echo "<h3>Testing Search Functionality:</h3>";
$searchResults = searchEmployees($conn, 'John');
echo "<p>Search for 'John': Found " . count($searchResults) . " results</p>";

$allEmployees = searchEmployees($conn, '');
echo "<p>Total employees in database: " . count($allEmployees) . "</p>";

echo "<hr>";
echo "<p><a href='employee.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Employee Management System</a></p>";
?>
