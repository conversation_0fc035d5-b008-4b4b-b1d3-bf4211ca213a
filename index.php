<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Travel Itinerary Maker</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <h2>Travel Itinerary Maker</h2>
    <form action="generate.php" method="POST">
        <label>Name:</label>
        <input type="text" name="name" required>

        <label>Position:</label>
        <input type="text" name="position" required>

        <label>Official Station:</label>
        <input type="text" name="station" required>

        <label>Date of Travel:</label>
        <input type="date" name="date" required>

        <label>Purpose of Travel:</label>
        <textarea name="purpose" required></textarea>

        <h3>Travel Segment (You may input multiple rows)</h3>
        <div id="travel-rows">
            <div class="travel-row">
                <input type="text" name="places[]" placeholder="Place Visited" required>
                <input type="time" name="departure[]" required>
                <input type="time" name="arrival[]" required>
                <input type="text" name="transport[]" placeholder="Means of Transport" required>
                <input type="number" name="transpo[]" placeholder="Transportation Cost" required>
                <input type="number" name="perdiem[]" placeholder="Per Diem" required>
            </div>
        </div>
        <button type="button" onclick="addRow()">+ Add Row</button><br><br>

        <input type="submit" value="Generate Itinerary">
    </form>

    <script>
        function addRow() {
            const container = document.getElementById('travel-rows');
            const row = document.createElement('div');
            row.classList.add('travel-row');
            row.innerHTML = `
                <input type="text" name="places[]" placeholder="Place Visited" required>
                <input type="time" name="departure[]" required>
                <input type="time" name="arrival[]" required>
                <input type="text" name="transport[]" placeholder="Means of Transport" required>
                <input type="number" name="transpo[]" placeholder="Transportation Cost" required>
                <input type="number" name="perdiem[]" placeholder="Per Diem" required>
            `;
            container.appendChild(row);
        }
    </script>
</body>
</html>
