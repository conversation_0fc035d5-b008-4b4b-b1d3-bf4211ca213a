/**
 * Modern Modal System for Employee Management System
 * Provides professional modal dialogs with animations and form handling
 */

class ModalSystem {
    constructor() {
        this.activeModal = null;
        this.init();
    }

    init() {
        // Create modal container if it doesn't exist
        if (!document.getElementById('modal-container')) {
            const container = document.createElement('div');
            container.id = 'modal-container';
            document.body.appendChild(container);
        }

        // Bind global event listeners
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.closeModal();
            }
        });

        // Bind click events for modal triggers
        this.bindModalTriggers();
    }

    bindModalTriggers() {
        // Bind all elements with data-modal attribute
        document.addEventListener('click', (e) => {
            const trigger = e.target.closest('[data-modal]');
            if (trigger) {
                e.preventDefault();
                const modalType = trigger.getAttribute('data-modal');
                const modalData = trigger.getAttribute('data-modal-data');
                this.openModal(modalType, modalData ? JSON.parse(modalData) : {});
            }
        });
    }

    openModal(type, data = {}) {
        const modalConfig = this.getModalConfig(type, data);
        if (!modalConfig) {
            console.error(`Modal type "${type}" not found`);
            return;
        }

        const modalHTML = this.createModalHTML(modalConfig);
        const container = document.getElementById('modal-container');
        container.innerHTML = modalHTML;

        // Show modal with animation
        setTimeout(() => {
            const overlay = container.querySelector('.modal-overlay');
            overlay.classList.add('active');
        }, 10);

        this.activeModal = type;
        this.bindModalEvents(container, modalConfig);

        // Dispatch custom event for modal opened
        document.dispatchEvent(new CustomEvent('modalOpened', {
            detail: { modal: container.querySelector('.modal'), type: type, data: data }
        }));

        // Focus first input
        setTimeout(() => {
            const firstInput = container.querySelector('input, select, textarea');
            if (firstInput) firstInput.focus();
        }, 300);
    }

    closeModal() {
        const container = document.getElementById('modal-container');
        const overlay = container.querySelector('.modal-overlay');
        
        if (overlay) {
            overlay.classList.remove('active');
            setTimeout(() => {
                container.innerHTML = '';
                this.activeModal = null;
            }, 300);
        }
    }

    createModalHTML(config) {
        return `
            <div class="modal-overlay">
                <div class="modal">
                    <div class="modal-header">
                        <h3 class="modal-title">${config.title}</h3>
                        <button type="button" class="modal-close" onclick="modalSystem.closeModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${config.body}
                    </div>
                    <div class="modal-footer">
                        ${config.footer}
                    </div>
                </div>
            </div>
        `;
    }

    bindModalEvents(container, config) {
        // Bind close events
        const overlay = container.querySelector('.modal-overlay');
        const closeBtn = container.querySelector('.modal-close');
        
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.closeModal();
            }
        });

        // Bind form submission
        const form = container.querySelector('form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit(form, config);
            });
        }

        // Bind custom events if provided
        if (config.events) {
            config.events.forEach(event => {
                const element = container.querySelector(event.selector);
                if (element) {
                    element.addEventListener(event.type, event.handler);
                }
            });
        }
    }

    handleFormSubmit(form, config) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('[type="submit"]');
        
        // Show loading state
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        }

        // Validate form if validator provided
        if (config.validator && !config.validator(formData)) {
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = config.submitText || 'Submit';
            }
            return;
        }

        // Submit form
        if (config.onSubmit) {
            config.onSubmit(formData, form);
        } else {
            // Default form submission
            form.submit();
        }
    }

    getModalConfig(type, data) {
        const configs = {
            'add-employee': {
                title: 'Add New Employee',
                body: this.getEmployeeFormHTML(),
                footer: `
                    <button type="button" class="btn btn-secondary" onclick="modalSystem.closeModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" form="employee-form">
                        <i class="fas fa-user-plus"></i> Add Employee
                    </button>
                `,
                submitText: '<i class="fas fa-user-plus"></i> Add Employee'
            },
            'edit-employee': {
                title: 'Edit Employee',
                body: this.getEmployeeFormHTML(data),
                footer: `
                    <button type="button" class="btn btn-secondary" onclick="modalSystem.closeModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" form="employee-form">
                        <i class="fas fa-save"></i> Update Employee
                    </button>
                `,
                submitText: '<i class="fas fa-save"></i> Update Employee'
            },
            'view-employee': {
                title: 'Employee Details',
                body: this.getEmployeeViewHTML(data),
                footer: `
                    <button type="button" class="btn btn-secondary" onclick="modalSystem.closeModal()">
                        Close
                    </button>
                    <button type="button" class="btn btn-warning" onclick="modalSystem.closeModal(); editEmployee(${data.id})">
                        <i class="fas fa-edit"></i> Edit Employee
                    </button>
                `,
                submitText: null
            },
            'add-travel': {
                title: 'Submit Travel Request',
                body: this.getTravelFormHTML(),
                footer: `
                    <button type="button" class="btn btn-secondary" onclick="modalSystem.closeModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" form="travel-form">
                        <i class="fas fa-paper-plane"></i> Submit Request
                    </button>
                `,
                submitText: '<i class="fas fa-paper-plane"></i> Submit Request'
            },
            'edit-travel': {
                title: 'Edit Travel Request',
                body: this.getTravelFormHTML(data),
                footer: `
                    <button type="button" class="btn btn-secondary" onclick="modalSystem.closeModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" form="travel-form">
                        <i class="fas fa-save"></i> Update Request
                    </button>
                `,
                submitText: '<i class="fas fa-save"></i> Update Request'
            },
            'add-leave': {
                title: 'Submit Leave Application',
                body: this.getLeaveFormHTML(),
                footer: `
                    <button type="button" class="btn btn-secondary" onclick="modalSystem.closeModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" form="leave-form">
                        <i class="fas fa-paper-plane"></i> Submit Application
                    </button>
                `,
                submitText: '<i class="fas fa-paper-plane"></i> Submit Application'
            },
            'edit-leave': {
                title: 'Edit Leave Application',
                body: this.getLeaveFormHTML(data),
                footer: `
                    <button type="button" class="btn btn-secondary" onclick="modalSystem.closeModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" form="leave-form">
                        <i class="fas fa-save"></i> Update Application
                    </button>
                `,
                submitText: '<i class="fas fa-save"></i> Update Application'
            },
            'confirm-delete': {
                title: 'Confirm Deletion',
                body: `
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                        <p class="text-lg mb-4">Are you sure you want to delete this ${data.type || 'item'}?</p>
                        <p class="text-sm text-gray-600">This action cannot be undone.</p>
                    </div>
                `,
                footer: `
                    <button type="button" class="btn btn-secondary" onclick="modalSystem.closeModal()">
                        Cancel
                    </button>
                    <button type="button" class="btn btn-danger" onclick="modalSystem.confirmDelete('${data.id}', '${data.type}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                `,
                onSubmit: (formData, form) => {
                    if (data.onConfirm) {
                        data.onConfirm();
                    }
                }
            }
        };

        return configs[type];
    }

    getEmployeeFormHTML(data = {}) {
        return `
            <form id="employee-form" method="post" action="employee.php">
                ${data.id ? `<input type="hidden" name="id" value="${data.id}">` : ''}
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="employee_id">Employee ID</label>
                        <input type="text" id="employee_id" name="employee_id" class="form-input"
                               value="${data.employee_id || ''}" ${data.id ? 'readonly' : ''} required
                               placeholder="e.g., EMP001, DOC001, NUR001">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="first_name">First Name</label>
                        <input type="text" id="first_name" name="first_name" class="form-input"
                               value="${data.first_name || ''}" required placeholder="Enter first name">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="last_name">Last Name</label>
                        <input type="text" id="last_name" name="last_name" class="form-input"
                               value="${data.last_name || ''}" required placeholder="Enter last name">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="email">Email Address</label>
                        <input type="email" id="email" name="email" class="form-input"
                               value="${data.email || ''}" required placeholder="Enter email address">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="phone">Phone Number</label>
                        <input type="tel" id="phone" name="phone" class="form-input"
                               value="${data.phone || ''}" placeholder="Enter phone number">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="position">Position</label>
                        <input type="text" id="position" name="position" class="form-input"
                               value="${data.position || ''}" required placeholder="Enter position">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="department">Department</label>
                        <select id="department" name="department" class="form-select" required>
                            <option value="">Select Department</option>
                            <option value="Administration" ${data.department === 'Administration' ? 'selected' : ''}>Administration</option>
                            <option value="Medical" ${data.department === 'Medical' ? 'selected' : ''}>Medical</option>
                            <option value="Nursing" ${data.department === 'Nursing' ? 'selected' : ''}>Nursing</option>
                            <option value="Emergency" ${data.department === 'Emergency' ? 'selected' : ''}>Emergency</option>
                            <option value="Surgery" ${data.department === 'Surgery' ? 'selected' : ''}>Surgery</option>
                            <option value="Radiology" ${data.department === 'Radiology' ? 'selected' : ''}>Radiology</option>
                            <option value="Laboratory" ${data.department === 'Laboratory' ? 'selected' : ''}>Laboratory</option>
                            <option value="Pharmacy" ${data.department === 'Pharmacy' ? 'selected' : ''}>Pharmacy</option>
                            <option value="IT" ${data.department === 'IT' ? 'selected' : ''}>IT</option>
                            <option value="Human Resources" ${data.department === 'Human Resources' ? 'selected' : ''}>Human Resources</option>
                            <option value="Finance" ${data.department === 'Finance' ? 'selected' : ''}>Finance</option>
                            <option value="Maintenance" ${data.department === 'Maintenance' ? 'selected' : ''}>Maintenance</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="hire_date">Hire Date</label>
                        <input type="date" id="hire_date" name="hire_date" class="form-input"
                               value="${data.hire_date || ''}" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="salary">Salary</label>
                        <input type="number" id="salary" name="salary" class="form-input" step="0.01" min="0"
                               value="${data.salary || ''}" required placeholder="Enter salary">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="status">Status</label>
                        <select id="status" name="status" class="form-select" required>
                            <option value="Active" ${data.status === 'Active' ? 'selected' : ''}>Active</option>
                            <option value="Inactive" ${data.status === 'Inactive' ? 'selected' : ''}>Inactive</option>
                        </select>
                    </div>
                </div>
            </form>
        `;
    }

    getEmployeeViewHTML(data = {}) {
        return `
            <div class="employee-details">
                <div class="detail-section">
                    <h4><i class="fas fa-user"></i> Personal Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Employee ID:</label>
                            <span class="employee-id">${data.employee_id || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Full Name:</label>
                            <span>${data.first_name || ''} ${data.last_name || ''}</span>
                        </div>
                        <div class="detail-item">
                            <label>Email:</label>
                            <span><a href="mailto:${data.email || ''}">${data.email || 'N/A'}</a></span>
                        </div>
                        <div class="detail-item">
                            <label>Phone:</label>
                            <span><a href="tel:${data.phone || ''}">${data.phone || 'N/A'}</a></span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4><i class="fas fa-briefcase"></i> Employment Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Position:</label>
                            <span>${data.position || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Department:</label>
                            <span>${data.department || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Hire Date:</label>
                            <span>${data.hire_date ? new Date(data.hire_date).toLocaleDateString() : 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Salary:</label>
                            <span class="salary-amount">$${data.salary ? parseFloat(data.salary).toLocaleString() : 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Status:</label>
                            <span class="status-badge status-${data.status ? data.status.toLowerCase() : 'inactive'}">${data.status || 'N/A'}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4><i class="fas fa-clock"></i> System Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Created:</label>
                            <span>${data.created_at ? new Date(data.created_at).toLocaleString() : 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Last Updated:</label>
                            <span>${data.updated_at ? new Date(data.updated_at).toLocaleString() : 'N/A'}</span>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .employee-details { padding: 0.5rem 0; }
                .detail-section { margin-bottom: 1.5rem; }
                .detail-section h4 {
                    color: #007bff;
                    margin-bottom: 0.75rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 1rem;
                    font-weight: 600;
                    border-bottom: 1px solid #eee;
                    padding-bottom: 0.5rem;
                }
                .detail-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 0.75rem;
                }
                .detail-item {
                    display: flex;
                    flex-direction: column;
                    gap: 0.2rem;
                }
                .detail-item label {
                    font-weight: 600;
                    color: #666;
                    font-size: 0.8rem;
                    text-transform: uppercase;
                }
                .detail-item span {
                    font-weight: 500;
                    color: #333;
                    font-size: 0.9rem;
                }
                .form-row {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 0.75rem;
                }
                .form-input, .form-select {
                    border: 1px solid #ccc;
                    padding: 0.5rem;
                    font-size: 0.9rem;
                }
                .form-input:focus, .form-select:focus {
                    border-color: #007bff;
                    outline: none;
                }
                .form-label {
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 0.3rem;
                    font-size: 0.85rem;
                }
            </style>
        `;
    }
                    <label class="form-label" for="position">Role/Position</label>
                    <input type="text" id="position" name="position" class="form-input"
                           value="${data.position || ''}" required
                           placeholder="e.g., Doctor, Nurse, Administrator">
                </div>
                <div class="form-group">
                    <label class="form-label" for="department">Department</label>
                    <input type="text" id="department" name="department" class="form-input"
                           value="${data.department || ''}" required
                           placeholder="e.g., Cardiology, Emergency, ICU">
                </div>
                <input type="hidden" name="submit_employee" value="1">
            </form>
        `;
    }

    getTravelFormHTML(data = {}) {
        // This will be populated with employee options from PHP
        return `
            <form id="travel-form" method="post" action="travel_leave.php">
                ${data.id ? `<input type="hidden" name="travel_id" value="${data.id}"><input type="hidden" name="is_update" value="1">` : ''}
                <div class="form-group">
                    <label class="form-label" for="employee_id">Staff Member</label>
                    <select id="employee_id" name="employee_id" class="form-select" required>
                        <option value="">Select Staff Member</option>
                        <!-- Employee options will be populated by PHP -->
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="destination">Destination</label>
                    <input type="text" id="destination" name="destination" class="form-input" 
                           value="${data.destination || ''}" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="purpose">Purpose</label>
                    <textarea id="purpose" name="purpose" class="form-textarea" required>${data.purpose || ''}</textarea>
                </div>
                <div class="form-group">
                    <label class="form-label" for="start_date">Start Date</label>
                    <input type="date" id="start_date" name="start_date" class="form-input" 
                           value="${data.start_date || ''}" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="end_date">End Date</label>
                    <input type="date" id="end_date" name="end_date" class="form-input" 
                           value="${data.end_date || ''}" required>
                </div>
                ${data.id ? `
                <div class="form-group">
                    <label class="form-label" for="status">Status</label>
                    <select id="status" name="status" class="form-select" required>
                        <option value="Pending" ${data.status === 'Pending' ? 'selected' : ''}>Pending</option>
                        <option value="Approved" ${data.status === 'Approved' ? 'selected' : ''}>Approved</option>
                        <option value="Rejected" ${data.status === 'Rejected' ? 'selected' : ''}>Rejected</option>
                    </select>
                </div>
                ` : ''}
                <input type="hidden" name="submit_travel" value="1">
            </form>
        `;
    }

    getLeaveFormHTML(data = {}) {
        return `
            <form id="leave-form" method="post" action="travel_leave.php">
                ${data.id ? `<input type="hidden" name="leave_id" value="${data.id}"><input type="hidden" name="is_update" value="1">` : ''}
                <div class="form-group">
                    <label class="form-label" for="employee_id">Staff Member</label>
                    <select id="employee_id" name="employee_id" class="form-select" required>
                        <option value="">Select Staff Member</option>
                        <!-- Employee options will be populated by PHP -->
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="leave_type">Leave Type</label>
                    <select id="leave_type" name="leave_type" class="form-select" required>
                        <option value="">Select Leave Type</option>
                        <option value="Annual" ${data.leave_type === 'Annual' ? 'selected' : ''}>Annual Leave</option>
                        <option value="Sick" ${data.leave_type === 'Sick' ? 'selected' : ''}>Sick Leave</option>
                        <option value="Medical" ${data.leave_type === 'Medical' ? 'selected' : ''}>Medical Leave</option>
                        <option value="Emergency" ${data.leave_type === 'Emergency' ? 'selected' : ''}>Emergency Leave</option>
                        <option value="Maternity" ${data.leave_type === 'Maternity' ? 'selected' : ''}>Maternity Leave</option>
                        <option value="Paternity" ${data.leave_type === 'Paternity' ? 'selected' : ''}>Paternity Leave</option>
                        <option value="Bereavement" ${data.leave_type === 'Bereavement' ? 'selected' : ''}>Bereavement Leave</option>
                        <option value="Study" ${data.leave_type === 'Study' ? 'selected' : ''}>Study Leave</option>
                        <option value="Conference" ${data.leave_type === 'Conference' ? 'selected' : ''}>Conference Leave</option>
                        <option value="Personal" ${data.leave_type === 'Personal' ? 'selected' : ''}>Personal Leave</option>
                        <option value="Other" ${data.leave_type === 'Other' ? 'selected' : ''}>Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="reason">Reason</label>
                    <textarea id="reason" name="reason" class="form-textarea" required>${data.reason || ''}</textarea>
                </div>
                <div class="form-group">
                    <label class="form-label" for="start_date">Start Date</label>
                    <input type="date" id="start_date" name="start_date" class="form-input" 
                           value="${data.start_date || ''}" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="end_date">End Date</label>
                    <input type="date" id="end_date" name="end_date" class="form-input" 
                           value="${data.end_date || ''}" required>
                </div>
                ${data.id ? `
                <div class="form-group">
                    <label class="form-label" for="status">Status</label>
                    <select id="status" name="status" class="form-select" required>
                        <option value="Pending" ${data.status === 'Pending' ? 'selected' : ''}>Pending</option>
                        <option value="Approved" ${data.status === 'Approved' ? 'selected' : ''}>Approved</option>
                        <option value="Rejected" ${data.status === 'Rejected' ? 'selected' : ''}>Rejected</option>
                    </select>
                </div>
                ` : ''}
                <input type="hidden" name="submit_leave" value="1">
            </form>
        `;
    }

    confirmDelete(id, type) {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'post';
        form.style.display = 'none';
        
        if (type === 'employee') {
            form.action = 'employees.php';
            form.innerHTML = `
                <input type="hidden" name="employee_id" value="${id}">
                <input type="hidden" name="delete" value="1">
            `;
        } else if (type === 'travel') {
            form.action = 'travel_leave.php';
            form.innerHTML = `
                <input type="hidden" name="travel_id" value="${id}">
                <input type="hidden" name="delete_travel" value="1">
            `;
        } else if (type === 'leave') {
            form.action = 'travel_leave.php';
            form.innerHTML = `
                <input type="hidden" name="leave_id" value="${id}">
                <input type="hidden" name="delete_leave" value="1">
            `;
        }
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Initialize modal system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.modalSystem = new ModalSystem();
    console.log('Modal system initialized');
});
