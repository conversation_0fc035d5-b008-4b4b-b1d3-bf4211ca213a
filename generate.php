<?php
$name = $_POST['name'];
$position = $_POST['position'];
$station = $_POST['station'];
$date = $_POST['date'];
$purpose = $_POST['purpose'];

$places = $_POST['places'];
$departure = $_POST['departure'];
$arrival = $_POST['arrival'];
$transport = $_POST['transport'];
$transpo = $_POST['transpo'];
$perdiem = $_POST['perdiem'];

$totalTranspo = array_sum($transpo);
$totalPerDiem = array_sum($perdiem);
$total = $totalTranspo + $totalPerDiem;
?>

<!DOCTYPE html>
<html>
<head>
    <title>Generated Itinerary</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <h2>ITINERARY OF TRAVEL</h2>
    <p><strong>Name:</strong> <?= htmlspecialchars($name) ?></p>
    <p><strong>Position:</strong> <?= htmlspecialchars($position) ?></p>
    <p><strong>Official Station:</strong> <?= htmlspecialchars($station) ?></p>
    <p><strong>Purpose of Travel:</strong> <?= htmlspecialchars($purpose) ?></p>
    <table border="1">
        <tr>
            <th>Place Visited</th>
            <th>Departure</th>
            <th>Arrival</th>
            <th>Transport</th>
            <th>Transportation</th>
            <th>Per Diem</th>
            <th>Total</th>
        </tr>
        <?php for ($i = 0; $i < count($places); $i++): ?>
        <tr>
            <td><?= htmlspecialchars($places[$i]) ?></td>
            <td><?= $departure[$i] ?></td>
            <td><?= $arrival[$i] ?></td>
            <td><?= htmlspecialchars($transport[$i]) ?></td>
            <td>PHP <?= number_format($transpo[$i], 2) ?></td>
            <td>PHP <?= number_format($perdiem[$i], 2) ?></td>
            <td>PHP <?= number_format($transpo[$i] + $perdiem[$i], 2) ?></td>
        </tr>
        <?php endfor; ?>
        <tr>
            <td colspan="4"><strong>Totals</strong></td>
            <td><strong>PHP <?= number_format($totalTranspo, 2) ?></strong></td>
            <td><strong>PHP <?= number_format($totalPerDiem, 2) ?></strong></td>
            <td><strong>PHP <?= number_format($total, 2) ?></strong></td>
        </tr>
    </table>

    <p>Prepared by: <?= htmlspecialchars($name) ?></p>
    <p>Approved by: <u>LUCILLE G. ROMINES, MD, FPCP</u></p>
</body>
</html>
