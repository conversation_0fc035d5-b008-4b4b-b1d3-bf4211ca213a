<?php
require_once 'database.php';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    switch ($_POST['action']) {
        case 'create':
            $data = [
                'employee_id' => $_POST['employee_id'],
                'first_name' => $_POST['first_name'],
                'last_name' => $_POST['last_name'],
                'email' => $_POST['email'],
                'phone' => $_POST['phone'],
                'position' => $_POST['position'],
                'department' => $_POST['department'],
                'hire_date' => $_POST['hire_date'],
                'salary' => $_POST['salary'],
                'status' => $_POST['status']
            ];
            $result = insertEmployee($conn, $data);
            echo json_encode($result);
            exit;

        case 'update':
            $id = $_POST['id'];
            $data = [
                'employee_id' => $_POST['employee_id'],
                'first_name' => $_POST['first_name'],
                'last_name' => $_POST['last_name'],
                'email' => $_POST['email'],
                'phone' => $_POST['phone'],
                'position' => $_POST['position'],
                'department' => $_POST['department'],
                'hire_date' => $_POST['hire_date'],
                'salary' => $_POST['salary'],
                'status' => $_POST['status']
            ];
            $result = updateEmployee($conn, $id, $data);
            echo json_encode($result);
            exit;

        case 'delete':
            $id = $_POST['id'];
            $result = deleteEmployee($conn, $id);
            echo json_encode($result);
            exit;

        case 'get':
            $id = $_POST['id'];
            $employee = getEmployeeById($conn, $id);
            echo json_encode($employee);
            exit;
    }
}

// Handle search
$search = isset($_GET['search']) ? $_GET['search'] : '';
$employees = searchEmployees($conn, $search);

// Get statistics
$totalEmployees = count(searchEmployees($conn, ''));
$activeEmployees = count(array_filter(searchEmployees($conn, ''), function($emp) {
    return $emp['status'] === 'Active';
}));
$inactiveEmployees = $totalEmployees - $activeEmployees;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Management - TEV Hospital System</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/modern-style.css">
    <style>
        /* Minimal Professional Design */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: white;
            border: 1px solid #ddd;
            padding: 1rem;
            text-align: center;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.85rem;
            color: #666;
        }

        .stat-icon {
            font-size: 1.5rem;
            color: #007bff;
            margin-bottom: 0.5rem;
        }

        .stat-card.success .stat-icon { color: #28a745; }
        .stat-card.warning .stat-icon { color: #ffc107; }

        .search-section {
            background: white;
            border: 1px solid #ddd;
            padding: 1rem;
            margin-bottom: 1.5rem;
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 250px;
            position: relative;
        }

        .search-input input {
            width: 100%;
            padding: 0.5rem 0.5rem 0.5rem 2rem;
            border: 1px solid #ccc;
            font-size: 0.9rem;
        }

        .search-input i {
            position: absolute;
            left: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .employee-table-container {
            background: white;
            border: 1px solid #ddd;
        }

        .table-header {
            background: #f5f5f5;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .employee-count {
            background: #007bff;
            color: white;
            padding: 0.2rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Simple Button Styles */
        .btn {
            padding: 0.4rem 0.8rem;
            font-size: 0.85rem;
            font-weight: 500;
            border: 1px solid;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }

        .btn-primary { background: #007bff; color: white; border-color: #007bff; }
        .btn-secondary { background: #6c757d; color: white; border-color: #6c757d; }
        .btn-info { background: #17a2b8; color: white; border-color: #17a2b8; }
        .btn-warning { background: #ffc107; color: #212529; border-color: #ffc107; }
        .btn-danger { background: #dc3545; color: white; border-color: #dc3545; }

        /* Simple Table */
        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 0.75rem 0.5rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.8rem;
            color: #333;
            border-bottom: 1px solid #ddd;
        }

        .table td {
            padding: 0.75rem 0.5rem;
            border-bottom: 1px solid #eee;
            font-size: 0.85rem;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- Top Navigation Bar -->
    <div class="topbar">
        <div class="topbar-title">
            <i class="fas fa-users-cog"></i>
            Employee Management System
        </div>
        <div class="topbar-status">
            <i class="fas fa-circle text-success"></i>
            System Online
        </div>
    </div>

    <!-- Main Content -->
    <div class="content">
        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-users stat-icon"></i>
                <div class="stat-number"><?php echo $totalEmployees; ?></div>
                <div class="stat-label">Total Employees</div>
            </div>
            <div class="stat-card success">
                <i class="fas fa-user-check stat-icon"></i>
                <div class="stat-number"><?php echo $activeEmployees; ?></div>
                <div class="stat-label">Active Employees</div>
            </div>
            <div class="stat-card warning">
                <i class="fas fa-user-times stat-icon"></i>
                <div class="stat-number"><?php echo $inactiveEmployees; ?></div>
                <div class="stat-label">Inactive Employees</div>
            </div>
        </div>

        <!-- Search and Actions Section -->
        <div class="search-section">
            <div class="search-input">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" class="form-input" placeholder="Search employees by name, ID, email, position, or department..." value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" data-modal="add-employee">
                    <i class="fas fa-user-plus"></i> Add Employee
                </button>
                <button class="btn btn-secondary" onclick="exportEmployees()">
                    <i class="fas fa-download"></i> Export
                </button>
                <button class="btn btn-info" onclick="refreshTable()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>

        <!-- Employee Table -->
        <div class="employee-table-container">
            <div class="table-header">
                <div class="table-title">
                    <i class="fas fa-table"></i>
                    Employee Directory
                </div>
                <div class="employee-count">
                    <?php echo count($employees); ?> employees found
                </div>
            </div>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Employee ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Position</th>
                            <th>Department</th>
                            <th>Hire Date</th>
                            <th>Salary</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="employeeTableBody">
                        <?php if (empty($employees)): ?>
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <div class="empty-state">
                                        <i class="fas fa-users text-muted" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                        <h4 class="text-muted">No employees found</h4>
                                        <p class="text-muted">Try adjusting your search criteria or add a new employee.</p>
                                    </div>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($employees as $employee): ?>
                                <tr>
                                    <td>
                                        <span class="employee-id"><?php echo htmlspecialchars($employee['employee_id']); ?></span>
                                    </td>
                                    <td>
                                        <div class="employee-name">
                                            <strong><?php echo htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']); ?></strong>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="mailto:<?php echo htmlspecialchars($employee['email']); ?>" class="text-primary">
                                            <?php echo htmlspecialchars($employee['email']); ?>
                                        </a>
                                    </td>
                                    <td>
                                        <a href="tel:<?php echo htmlspecialchars($employee['phone']); ?>" class="text-secondary">
                                            <?php echo htmlspecialchars($employee['phone']); ?>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="position-badge"><?php echo htmlspecialchars($employee['position']); ?></span>
                                    </td>
                                    <td>
                                        <span class="department-badge"><?php echo htmlspecialchars($employee['department']); ?></span>
                                    </td>
                                    <td>
                                        <?php echo date('M d, Y', strtotime($employee['hire_date'])); ?>
                                    </td>
                                    <td>
                                        <span class="salary-amount">$<?php echo number_format($employee['salary'], 2); ?></span>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo strtolower($employee['status']); ?>">
                                            <?php echo $employee['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical">
                                            <button class="btn btn-sm btn-info" onclick="viewEmployee(<?php echo $employee['id']; ?>)" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" onclick="editEmployee(<?php echo $employee['id']; ?>)" title="Edit Employee">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteEmployee(<?php echo $employee['id']; ?>, '<?php echo htmlspecialchars($employee['first_name'] . ' ' . $employee['last_name']); ?>')" title="Delete Employee">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <!-- Scripts -->
    <script src="assets/js/modal-system.js"></script>
    <script>
        // Initialize modal system
        const modalSystem = new ModalSystem();

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value;
            if (searchTerm.length >= 2 || searchTerm.length === 0) {
                window.location.href = `employee.php?search=${encodeURIComponent(searchTerm)}`;
            }
        });

        // Employee CRUD Functions
        function viewEmployee(id) {
            fetch('employee.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get&id=${id}`
            })
            .then(response => response.json())
            .then(employee => {
                if (employee) {
                    modalSystem.openModal('view-employee', employee);
                } else {
                    showNotification('Employee not found', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error loading employee details', 'error');
            });
        }

        function editEmployee(id) {
            fetch('employee.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get&id=${id}`
            })
            .then(response => response.json())
            .then(employee => {
                if (employee) {
                    modalSystem.openModal('edit-employee', employee);
                } else {
                    showNotification('Employee not found', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error loading employee details', 'error');
            });
        }

        function deleteEmployee(id, name) {
            modalSystem.openModal('confirm-delete', {
                id: id,
                type: 'employee',
                name: name,
                onConfirm: () => {
                    fetch('employee.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `action=delete&id=${id}`
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            showNotification(result.message, 'success');
                            setTimeout(() => {
                                window.location.reload();
                            }, 1500);
                        } else {
                            showNotification(result.message, 'error');
                        }
                        modalSystem.closeModal();
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('Error deleting employee', 'error');
                        modalSystem.closeModal();
                    });
                }
            });
        }

        // Utility functions
        function refreshTable() {
            window.location.reload();
        }

        function exportEmployees() {
            // Simple CSV export
            const table = document.querySelector('.table');
            const rows = Array.from(table.querySelectorAll('tr'));
            const csv = rows.map(row => {
                const cells = Array.from(row.querySelectorAll('th, td'));
                return cells.slice(0, -1).map(cell => `"${cell.textContent.trim()}"`).join(',');
            }).join('\n');

            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `employees_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            // Add notification styles if not already present
            if (!document.getElementById('notification-styles')) {
                const style = document.createElement('style');
                style.id = 'notification-styles';
                style.textContent = `
                    .notification {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        padding: 1rem 1.5rem;
                        border-radius: 8px;
                        color: white;
                        font-weight: 500;
                        z-index: 10000;
                        animation: slideIn 0.3s ease-out;
                        max-width: 400px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    }
                    .notification-success { background-color: var(--success-color); }
                    .notification-error { background-color: var(--danger-color); }
                    .notification-info { background-color: var(--primary-color); }
                    .notification-content { display: flex; align-items: center; gap: 0.5rem; }
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideIn 0.3s ease-out reverse';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Handle form submissions
        document.addEventListener('modalOpened', function(e) {
            const modal = e.detail.modal;
            const form = modal.querySelector('form');

            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(form);
                    const action = formData.get('id') ? 'update' : 'create';
                    formData.append('action', action);

                    // Convert FormData to URLSearchParams
                    const params = new URLSearchParams();
                    for (let [key, value] of formData.entries()) {
                        params.append(key, value);
                    }

                    fetch('employee.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: params.toString()
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            showNotification(result.message, 'success');
                            modalSystem.closeModal();
                            setTimeout(() => {
                                window.location.reload();
                            }, 1500);
                        } else {
                            showNotification(result.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('Error saving employee', 'error');
                    });
                });
            }
        });
    </script>

    <!-- Simple Additional Styles -->
    <style>
        /* Simple Status Badges */
        .status-badge {
            padding: 0.2rem 0.5rem;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active { background: #28a745; color: white; }
        .status-inactive { background: #dc3545; color: white; }

        /* Simple Employee ID */
        .employee-id {
            font-family: monospace;
            font-weight: 600;
            color: #007bff;
            background: #f8f9fa;
            padding: 0.2rem 0.4rem;
            font-size: 0.8rem;
        }

        /* Simple Badges */
        .position-badge, .department-badge {
            background: #6c757d;
            color: white;
            padding: 0.2rem 0.4rem;
            font-size: 0.75rem;
        }

        .salary-amount {
            font-weight: 600;
            color: #28a745;
        }

        /* Empty State */
        .empty-state {
            padding: 2rem;
            text-align: center;
            color: #666;
        }

        /* Simple Action Buttons */
        .btn-group-vertical {
            display: flex;
            flex-direction: column;
            gap: 0.2rem;
        }

        .btn-group-vertical .btn {
            min-width: 30px;
            padding: 0.3rem;
            justify-content: center;
        }

        .btn-sm {
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
        }

        /* Simple Links */
        .text-primary { color: #007bff !important; text-decoration: none; }
        .text-primary:hover { text-decoration: underline; }
        .text-secondary { color: #666 !important; text-decoration: none; }
        .text-secondary:hover { text-decoration: underline; }

        /* Simple Employee Name */
        .employee-name strong {
            color: #333;
            font-weight: 600;
        }

        /* Simple Topbar */
        .topbar {
            background: white;
            border-bottom: 1px solid #ddd;
            box-shadow: none;
            padding: 0.75rem 1.5rem;
        }

        .topbar-title {
            color: #333;
            font-size: 1.1rem;
        }

        .topbar-title i {
            color: #007bff;
        }

        .text-success { color: #28a745 !important; }

        /* Content spacing */
        .content {
            padding: 1.5rem;
            max-width: 1200px;
            margin: 0 auto;
        }
    </style>
</body>
</html>