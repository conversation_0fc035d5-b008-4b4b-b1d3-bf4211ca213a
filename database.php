<?php
// Database Connection String for PDO (PHP Data Objects)
$connectionString = "mysql:host=localhost;dbname=bdh_travel";
$username = "root";
$password = "";

try {
    $conn = new PDO($connectionString, $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create employees table if it doesn't exist
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS employees (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id VARCHAR(20) UNIQUE NOT NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VA<PERSON>HAR(50) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        phone VARCHAR(20),
        position VARCHAR(100),
        department VARCHAR(100),
        hire_date DATE,
        salary DECIMAL(10,2),
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    $conn->exec($createTableSQL);

} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Employee Management Functions

// Insert Employee
function insertEmployee($conn, $data) {
    try {
        $sql = "INSERT INTO employees (employee_id, first_name, last_name, email, phone, position, department, hire_date, salary, status)
                VALUES (:employee_id, :first_name, :last_name, :email, :phone, :position, :department, :hire_date, :salary, :status)";

        $stmt = $conn->prepare($sql);
        $stmt->execute([
            ':employee_id' => $data['employee_id'],
            ':first_name' => $data['first_name'],
            ':last_name' => $data['last_name'],
            ':email' => $data['email'],
            ':phone' => $data['phone'],
            ':position' => $data['position'],
            ':department' => $data['department'],
            ':hire_date' => $data['hire_date'],
            ':salary' => $data['salary'],
            ':status' => $data['status']
        ]);

        return ['success' => true, 'message' => 'Employee added successfully'];
    } catch(PDOException $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

// Update Employee
function updateEmployee($conn, $id, $data) {
    try {
        $sql = "UPDATE employees SET
                employee_id = :employee_id,
                first_name = :first_name,
                last_name = :last_name,
                email = :email,
                phone = :phone,
                position = :position,
                department = :department,
                hire_date = :hire_date,
                salary = :salary,
                status = :status
                WHERE id = :id";

        $stmt = $conn->prepare($sql);
        $stmt->execute([
            ':id' => $id,
            ':employee_id' => $data['employee_id'],
            ':first_name' => $data['first_name'],
            ':last_name' => $data['last_name'],
            ':email' => $data['email'],
            ':phone' => $data['phone'],
            ':position' => $data['position'],
            ':department' => $data['department'],
            ':hire_date' => $data['hire_date'],
            ':salary' => $data['salary'],
            ':status' => $data['status']
        ]);

        return ['success' => true, 'message' => 'Employee updated successfully'];
    } catch(PDOException $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

// Delete Employee
function deleteEmployee($conn, $id) {
    try {
        $sql = "DELETE FROM employees WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->execute([':id' => $id]);

        return ['success' => true, 'message' => 'Employee deleted successfully'];
    } catch(PDOException $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

// Search Employees
function searchEmployees($conn, $search = '') {
    try {
        if (empty($search)) {
            $sql = "SELECT * FROM employees ORDER BY created_at DESC";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
        } else {
            $sql = "SELECT * FROM employees
                    WHERE first_name LIKE :search
                    OR last_name LIKE :search
                    OR employee_id LIKE :search
                    OR email LIKE :search
                    OR position LIKE :search
                    OR department LIKE :search
                    ORDER BY created_at DESC";
            $stmt = $conn->prepare($sql);
            $stmt->execute([':search' => '%' . $search . '%']);
        }

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        return [];
    }
}

// Get Employee by ID
function getEmployeeById($conn, $id) {
    try {
        $sql = "SELECT * FROM employees WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->execute([':id' => $id]);

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        return null;
    }
}
?>


