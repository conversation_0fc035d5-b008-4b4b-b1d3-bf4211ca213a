/* Hospital Management System - Professional CSS Framework */

:root {
    /* Modern Professional Color System */
    --primary-color: #0d6efd;        /* <PERSON>trap Primary Blue */
    --secondary-color: #6c757d;      /* Professional Gray */
    --accent-color: #0dcaf0;         /* Light Blue Accent */
    --background-color: #f8f9fa;     /* Light Gray Background */
    --card-background: #ffffff;      /* White Cards */
    --text-primary: #212529;         /* Dark Gray Text */
    --text-secondary: #6c757d;       /* Medium Gray Text */
    --text-muted: #adb5bd;           /* Light Gray Text */
    --border-color: #dee2e6;         /* Light Border */
    --success: #198754;              /* Green */
    --warning: #ffc107;              /* Yellow */
    --danger: #dc3545;               /* Red */
    --info: #0dcaf0;                 /* Cyan */

    /* Legacy compatibility - Modern Professional */
    --primary: #0d6efd;              /* Bootstrap Blue */
    --primary-dark: #0b5ed7;         /* Darker Blue */
    --primary-light: #6ea8fe;        /* Light Blue */
    --secondary: #6c757d;            /* Professional Gray */
    --secondary-dark: #5c636a;       /* Dark Gray */
    --accent: #0dcaf0;               /* Cyan accent */
    --danger: #dc3545;               /* Bootstrap Red */
    --danger-dark: #bb2d3b;          /* Dark Red */
    --warning: #ffc107;              /* Bootstrap Yellow */
    --success: #198754;              /* Bootstrap Green */
    --info: #0dcaf0;                 /* Bootstrap Cyan */

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e0;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Medical compatibility */
    --medical-bg: var(--background-color);
    --medical-light: var(--gray-100);
    --medical-border: var(--border-color);
    --medical-text: var(--text-secondary);
    --medical-dark: var(--text-primary);

    /* Compact Professional Typography */
    --font-family: 'Poppins', 'Inter', 'Segoe UI', 'Roboto', system-ui, sans-serif;
    --font-family-mono: 'Source Code Pro', 'Consolas', monospace;
    --font-size-xs: 0.7rem;      /* 11px - Very small labels */
    --font-size-sm: 0.8rem;      /* 13px - Small text */
    --font-size-base: 0.85rem;   /* 14px - Body text */
    --font-size-md: 0.9rem;      /* 14px - Standard text */
    --font-size-lg: 1rem;        /* 16px - Subheadings */
    --font-size-xl: 1.1rem;      /* 18px - Card titles */
    --font-size-2xl: 1.25rem;    /* 20px - Page titles */
    --font-size-3xl: 1.4rem;     /* 22px - Section headers */
    --font-size-4xl: 1.6rem;     /* 26px - Main headers */
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: #f8f9fa;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    min-height: 100vh;
}

/* Layout Components */
.app-layout {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 280px;
    background: var(--card-background);
    color: var(--text-primary);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 40;
    box-shadow: 4px 0 20px rgba(13, 110, 253, 0.15);
    border-right: 3px solid var(--primary-color);
}

.sidebar-header {
    padding: var(--space-6);
    border-bottom: 2px solid var(--border-color);
    background: var(--card-background);
}

.sidebar-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-3);
    color: var(--text-primary);
}

.sidebar-title i {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
}

.sidebar-nav {
    flex: 1;
    padding: var(--space-4) 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--space-4) var(--space-6);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background: linear-gradient(90deg, rgba(13, 110, 253, 0.1) 0%, rgba(13, 110, 253, 0.05) 100%);
    color: var(--text-primary);
    border-left-color: var(--primary-color);
    transform: translateX(5px);
}

.nav-link.active {
    background: linear-gradient(90deg, rgba(13, 110, 253, 0.15) 0%, rgba(13, 110, 253, 0.1) 100%);
    color: var(--text-primary);
    border-left-color: var(--primary-color);
    box-shadow: inset 0 0 20px rgba(13, 110, 253, 0.2);
}

.nav-link i {
    margin-right: var(--space-3);
    font-size: var(--font-size-lg);
    width: 20px;
    text-align: center;
}

.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
}

.topbar {
    background: var(--card-background);
    color: var(--text-primary);
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
    box-shadow: 0 0 15px rgba(0,0,0,0.1);
    border: 2px solid var(--primary-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 30;
}

.page-header {
    background: var(--card-background);
    color: var(--text-primary);
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
    box-shadow: 0 0 15px rgba(0,0,0,0.1);
    border: 2px solid var(--primary-color);
}

.topbar-title,
.page-header h1 {
    font-size: 1.5rem;
    margin: 0;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.topbar-title i,
.page-header h1 i {
    color: var(--primary-color);
}

.topbar-status {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.content {
    flex: 1;
    padding: var(--space-4) var(--space-6);
    max-width: 1400px;
    margin: 20px auto;
    width: 100%;
}

.container {
    max-width: 1400px;
    margin: 20px auto;
    padding: 1rem;
}

/* Professional Card Components */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
    background: var(--card-background);
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 0 25px rgba(0,0,0,0.08);
}

.card-header {
    padding: var(--space-6);
    background-color: var(--primary-color);
    color: white;
    border-bottom: none;
    position: relative;
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: white;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.card-title i {
    color: white;
    font-size: var(--font-size-lg);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-50);
}

/* Professional Button Components */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 0.375rem;
    font-weight: 500;
    border: 1px solid transparent;
    cursor: pointer;
    text-decoration: none;
    transition: all var(--transition-fast);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn i {
    margin-right: var(--space-2);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
}

.btn-group-vertical {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--white);
    border-color: var(--secondary-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
}

.btn-success {
    background-color: var(--success);
    color: var(--white);
    border-color: var(--success);
}

.btn-success:hover:not(:disabled) {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-danger {
    background-color: var(--danger);
    color: var(--white);
    border-color: var(--danger);
}

.btn-danger:hover:not(:disabled) {
    background-color: var(--danger-dark);
    border-color: var(--danger-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-sm {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-lg);
}

/* Professional Form Components */
.form-group {
    margin-bottom: var(--space-4);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.form-input,
.form-select,
.form-textarea,
.form-control {
    width: 100%;
    padding: var(--space-3);
    font-size: 0.875rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    background-color: var(--white);
    transition: all var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus,
.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Professional Table Components - Stand Out Design */
.table-container {
    background-color: var(--card-background);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 0 25px rgba(13, 110, 253, 0.15);
    border: 2px solid rgba(13, 110, 253, 0.1);
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
    margin-bottom: 0;
    background: white;
}

.table th {
    font-weight: 600;
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
    padding: 1rem 0.75rem;
    font-size: var(--font-size-sm);
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table td {
    padding: 0.875rem 0.75rem;
    vertical-align: middle;
    color: var(--text-primary);
    border-bottom: 1px solid rgba(13, 110, 253, 0.1);
    font-size: var(--font-size-sm);
    border-top: none;
    background: white;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: rgba(13, 110, 253, 0.03) !important;
}

.table-striped > tbody > tr:nth-of-type(even) {
    background-color: white !important;
}

.table-hover > tbody > tr:hover {
    background-color: rgba(13, 110, 253, 0.12) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.15);
    transition: all 0.2s ease;
}

.table-responsive {
    max-height: 70vh;
    overflow-y: auto;
    scrollbar-width: thin;
}

.table-responsive::-webkit-scrollbar {
    width: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: rgba(13, 110, 253, 0.1);
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #0b5ed7 0%, #0a58ca 100%);
}

.action-cell {
    white-space: nowrap;
    width: 100px;
}

.search-form .input-group {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Status Badges */
.badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
}

.badge-success {
    background-color: var(--success);
    color: white;
}

.badge-warning {
    background-color: var(--warning);
    color: #212529;
}

.badge-danger {
    background-color: var(--danger);
    color: white;
}

.badge-primary {
    background-color: var(--primary-color);
    color: white;
}

.badge-secondary {
    background-color: var(--secondary-color);
    color: white;
}

/* Compact Layout Styles */
.py-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: var(--font-size-xs);
    border-radius: 0.25rem;
}

.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}

.input-group .btn {
    position: relative;
    z-index: 2;
}

/* Enhanced Table Styling - Stand Out Design */
.table-responsive {
    max-height: 70vh;
    overflow-y: auto;
    border-radius: 15px;
    box-shadow: 0 0 25px rgba(13, 110, 253, 0.15);
    border: 2px solid rgba(13, 110, 253, 0.1);
    background: white;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    position: sticky;
    top: 0;
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%) !important;
    color: white !important;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Table Card Enhancement */
.card .table-responsive {
    margin: -1px;
    border-radius: 15px;
}

.card-body {
    padding: 0;
}

.card-body .table-responsive {
    border-radius: 15px;
}

h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin: 0;
}

.d-flex {
    display: flex !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.align-items-center {
    align-items: center !important;
}

.me-2 {
    margin-right: 0.5rem !important;
}

.ms-2 {
    margin-left: 0.5rem !important;
}

/* Alert Components */
.alert {
    padding: var(--space-4);
    border-radius: var(--radius);
    margin-bottom: var(--space-4);
    display: flex;
    align-items: center;
    border-left: 4px solid;
}

.alert i {
    margin-right: var(--space-3);
    font-size: var(--font-size-lg);
}

.alert-success {
    background-color: #f0fdf4;
    color: #166534;
    border-left-color: var(--success);
}

.alert-error {
    background-color: #fef2f2;
    color: #991b1b;
    border-left-color: var(--danger);
}

.alert-warning {
    background-color: #fffbeb;
    color: #92400e;
    border-left-color: var(--warning);
}

.alert-info {
    background-color: #eff6ff;
    color: #1e40af;
    border-left-color: var(--info);
}

/* Modal Components */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    backdrop-filter: blur(4px);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background-color: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all var(--transition-normal);
}

.modal-overlay.active .modal {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: var(--white);
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--white);
    font-size: var(--font-size-xl);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius);
    transition: all var(--transition-fast);
    opacity: 0.8;
}

.modal-close:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: var(--space-6);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-50);
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
}

/* Search Components */
.search-container {
    position: relative;
    margin-bottom: var(--space-6);
}

.search-input {
    width: 100%;
    padding: var(--space-3) var(--space-4) var(--space-3) var(--space-12);
    font-size: var(--font-size-base);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background-color: var(--white);
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-icon {
    position: absolute;
    left: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    font-size: var(--font-size-lg);
}

/* Badge Components */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-success {
    background-color: #dcfce7;
    color: #166534;
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-danger {
    background-color: #fecaca;
    color: #991b1b;
}

.badge-info {
    background-color: #dbeafe;
    color: #1e40af;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-sm { font-size: var(--font-size-sm); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }

/* Grid System */
.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-6 {
    gap: var(--space-6);
}

/* Responsive grid classes */
@media (min-width: 768px) {
    .md\\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

.space-y-2 > * + * {
    margin-top: var(--space-2);
}

.mr-3 {
    margin-right: var(--space-3);
}

.ml-2 {
    margin-left: var(--space-2);
}

.p-0 {
    padding: 0;
}

.py-12 {
    padding-top: var(--space-12);
    padding-bottom: var(--space-12);
}

.w-8 {
    width: 2rem;
}

.h-8 {
    height: 2rem;
}

.rounded-full {
    border-radius: var(--radius-full);
}

/* Color utilities */
.text-primary { color: var(--primary); }
.text-secondary { color: var(--gray-600); }
.text-success { color: var(--success); }
.text-danger { color: var(--danger); }
.text-warning { color: var(--warning); }
.text-info { color: var(--info); }
.text-gray-400 { color: var(--gray-400); }
.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-900 { color: var(--gray-900); }

.bg-primary { background-color: var(--primary); }
.bg-gray-50 { background-color: var(--gray-50); }

/* Dashboard Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, var(--white) 0%, var(--medical-light) 100%);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    border-left: 4px solid var(--primary);
    box-shadow: 0 2px 10px rgba(0, 102, 204, 0.08);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    opacity: 0.1;
    border-radius: 50%;
    transform: translate(20px, -20px);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 102, 204, 0.15);
    border-left-color: var(--secondary);
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-dark);
    margin: 0;
    line-height: 1;
}

.stat-label {
    font-size: var(--font-size-xs);
    color: var(--medical-text);
    margin: var(--space-1) 0 0 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.stat-icon {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    font-size: var(--font-size-xl);
    color: var(--primary);
    opacity: 0.7;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    border: none;
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    transition: all var(--transition-fast);
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.2);
}

.dropdown-toggle:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white);
    border: 2px solid var(--medical-border);
    border-radius: var(--radius);
    box-shadow: 0 8px 25px rgba(0, 102, 204, 0.15);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: var(--space-3) var(--space-4);
    color: var(--medical-dark);
    text-decoration: none;
    font-size: var(--font-size-sm);
    border-bottom: 1px solid var(--medical-border);
    transition: all var(--transition-fast);
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
}

.dropdown-item i {
    margin-right: var(--space-2);
    width: 16px;
    text-align: center;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-6);
}

.dashboard-section {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    border: 2px solid var(--medical-border);
    border-left: 4px solid var(--primary);
    box-shadow: 0 2px 10px rgba(0, 102, 204, 0.08);
}

.section-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0 0 var(--space-4) 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.section-title i {
    color: var(--secondary);
}

/* Additional utility classes */
.space-y-2 > * + * { margin-top: var(--space-2); }
.space-y-3 > * + * { margin-top: var(--space-3); }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }

.font-medium { font-weight: 500; }
.font-bold { font-weight: 700; }

.bg-medical-light { background-color: var(--medical-light); }
.text-medical-text { color: var(--medical-text); }
.text-primary-dark { color: var(--primary-dark); }

.rounded-lg { border-radius: var(--radius-lg); }
.rounded { border-radius: var(--radius); }

/* Status badge improvements */
.status-badge {
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-approved {
    background-color: #dcfce7;
    color: #166534;
}

.status-rejected {
    background-color: #fecaca;
    color: #991b1b;
}

/* Medical-themed enhancements */
.medical-pulse {
    animation: medicalPulse 2s infinite;
}

@keyframes medicalPulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.medical-gradient {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
}

.medical-shadow {
    box-shadow: 0 4px 20px rgba(0, 102, 204, 0.15);
}

/* Compact spacing for professional look */
.content {
    padding: var(--space-4) var(--space-6);
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Professional table styling */
.table th {
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: var(--space-3) var(--space-4);
}

.table td {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-sm);
}

/* Compact form styling */
.form-input,
.form-select,
.form-textarea {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-sm);
}

.form-label {
    font-size: var(--font-size-xs);
    font-weight: 600;
    margin-bottom: var(--space-1);
}

.form-group {
    margin-bottom: var(--space-4);
}

/* Compact button styling */
.btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.btn-sm {
    padding: var(--space-1) var(--space-3);
    font-size: var(--font-size-xs);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .grid-cols-3 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .grid-cols-2 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .md\\:grid-cols-3 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\\:grid-cols-2 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }
    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .content {
        padding: var(--space-4);
    }
}

@media (max-width: 768px) {
    .grid-cols-3,
    .grid-cols-2,
    .md\\:grid-cols-3,
    .md\\:grid-cols-2 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .modal {
        width: 95%;
        margin: var(--space-4);
    }

    .modal-body {
        padding: var(--space-4);
    }

    .modal-header,
    .modal-footer {
        padding: var(--space-4);
    }

    .topbar {
        padding: var(--space-3) var(--space-4);
    }

    .topbar-title {
        font-size: var(--font-size-xl);
    }
}
